import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { locales, getDirection, type Locale } from '../../lib/i18n';
import PerformanceInitializer from '../../components/PerformanceInitializer';
import '../globals.css';

// إعدادات الأداء والكاش
export const revalidate = 3600; // إعادة التحقق كل ساعة
export const fetchCache = 'auto'; // استخدام الكاش التلقائي
export const runtime = 'nodejs'; // استخدام Node.js runtime
export const preferredRegion = 'auto'; // اختيار المنطقة تلقائياً

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export const metadata: Metadata = {
  title: 'DROOB HAJER ',
  description: 'موقع متخصص في معدات  والفنادق',
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale: localeParam } = await params;

  // التحقق من صحة اللغة
  if (!locales.includes(localeParam as Locale)) {
    notFound();
  }

  const locale = localeParam as Locale;
  const direction = getDirection(locale);

  return (
    <div lang={locale} dir={direction} className={`${direction === 'rtl' ? 'rtl' : 'ltr'} font-tajawal min-h-screen`}>
      <PerformanceInitializer />
      {children}
    </div>
  );
}
